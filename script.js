// ScamShield JavaScript Functionality

// API Configuration
const API_BASE_URL = 'api.php';

// API Helper Functions
async function makeAPIRequest(endpoint, data = null, method = 'GET') {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }

        const url = `${API_BASE_URL}?endpoint=${endpoint}`;
        const response = await fetch(url, options);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}

// DOM Elements
const checkForm = document.getElementById('checkForm');
const reportForm = document.getElementById('reportForm');
const checkResults = document.getElementById('checkResults');
const reportResults = document.getElementById('reportResults');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadStats();
});

// Event Listeners
function initializeEventListeners() {
    // Check form submission
    if (checkForm) {
        checkForm.addEventListener('submit', handleCheckSubmission);
    }
    
    // Report form submission
    if (reportForm) {
        reportForm.addEventListener('submit', handleReportSubmission);
    }
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Handle check form submission
async function handleCheckSubmission(e) {
    e.preventDefault();

    const searchType = document.getElementById('searchType').value;
    const searchValue = document.getElementById('searchValue').value.trim();

    if (!searchType || !searchValue) {
        showAlert(checkResults, 'Please fill in all fields.', 'warning');
        return;
    }

    // Show loading
    showLoading(checkResults);

    try {
        const response = await makeAPIRequest('search', {
            type: searchType,
            value: searchValue
        }, 'POST');

        if (response.success) {
            displayCheckResults(response.results, searchValue, searchType);
        } else {
            showAlert(checkResults, response.error || 'Search failed. Please try again.', 'danger');
        }
    } catch (error) {
        console.error('Search error:', error);
        showAlert(checkResults, 'Search failed. Please check your connection and try again.', 'danger');
    }
}

// Handle report form submission
async function handleReportSubmission(e) {
    e.preventDefault();

    const reportType = document.getElementById('reportType').value;
    const reportContact = document.getElementById('reportContact').value.trim();
    const reportDescription = document.getElementById('reportDescription').value.trim();
    const reportAmount = document.getElementById('reportAmount').value.trim();

    if (!reportType || !reportContact || !reportDescription) {
        showAlert(reportResults, 'Please fill in all required fields.', 'warning');
        return;
    }

    // Show loading
    showLoading(reportResults);

    try {
        const response = await makeAPIRequest('report', {
            type: reportType,
            contact: reportContact,
            description: reportDescription,
            amount: reportAmount || ''
        }, 'POST');

        if (response.success) {
            displayReportSuccess(response);
            reportForm.reset();
            loadStats(); // Refresh stats
        } else {
            showAlert(reportResults, response.error || 'Report submission failed. Please try again.', 'danger');
        }
    } catch (error) {
        console.error('Report error:', error);
        showAlert(reportResults, 'Report submission failed. Please check your connection and try again.', 'danger');
    }
}

// Load statistics from API
async function loadStats() {
    try {
        const response = await makeAPIRequest('stats');
        if (response.success) {
            updateStatsDisplay(response.stats);
        }
    } catch (error) {
        console.error('Failed to load stats:', error);
    }
}

// Update stats display (for future use)
function updateStatsDisplay(stats) {
    console.log('Database Stats:', stats);
    // You can use these stats to update UI elements in the future
}

// Display check results
function displayCheckResults(results, searchValue, searchType) {
    checkResults.style.display = 'block';
    
    if (results.length > 0) {
        let html = `
            <div class="alert alert-danger fade-in">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>⚠️ SCAM ALERT!</h5>
                <p><strong>"${searchValue}"</strong> has been reported as a scam ${results.length} time(s).</p>
            </div>
        `;
        
        results.forEach(result => {
            html += `
                <div class="search-result danger fade-in">
                    <h6><i class="fas fa-shield-alt text-danger me-2"></i>Reported Scam</h6>
                    <p><strong>Type:</strong> ${capitalizeFirst(result.type)}</p>
                    <p><strong>Contact:</strong> ${result.contact}</p>
                    <p><strong>Description:</strong> ${result.description}</p>
                    <p><strong>Amount Requested:</strong> ${result.amount || 'Not specified'}</p>
                    <p><strong>Date Reported:</strong> ${formatDate(result.date_reported)}</p>
                    <p><strong>Total Reports:</strong> ${result.report_count}</p>
                </div>
            `;
        });
        
        html += `
            <div class="alert alert-info fade-in">
                <h6><i class="fas fa-lightbulb me-2"></i>What to do:</h6>
                <ul class="mb-0">
                    <li>Do NOT respond to this scammer</li>
                    <li>Do NOT send money or personal information</li>
                    <li>Block the contact immediately</li>
                    <li>Report to local authorities if threatened</li>
                </ul>
            </div>
        `;
    } else {
        html = `
            <div class="alert alert-success fade-in">
                <h5><i class="fas fa-check-circle me-2"></i>Good News!</h5>
                <p><strong>"${searchValue}"</strong> is not in our scam database.</p>
                <p>However, stay vigilant! If you suspect this might be a scam, trust your instincts.</p>
            </div>
            <div class="alert alert-warning fade-in">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>Still suspicious?</h6>
                <p>If you believe this is a scam that hasn't been reported yet, please help others by reporting it below.</p>
                <a href="#report" class="btn btn-warning">Report This Scam</a>
            </div>
        `;
    }
    
    checkResults.innerHTML = html;
}

// Display report success
function displayReportSuccess(response) {
    const isUpdate = response.result === 'updated';
    const html = `
        <div class="report-success fade-in">
            <i class="fas fa-check-circle"></i>
            <h5>Thank You for Your Report!</h5>
            <p>Your report has been successfully ${isUpdate ? 'updated' : 'submitted'} and ${isUpdate ? 'merged with existing reports' : 'added to our database'}.</p>
            <p>This ${isUpdate ? 'contact' : 'scam'} now has <strong>${response.report_count}</strong> report${response.report_count > 1 ? 's' : ''}.</p>
            <p>You're helping protect others from scams and blackmail attempts.</p>
            <div class="mt-3">
                <a href="#check" class="btn btn-primary me-2">Check Another</a>
                <a href="#learn" class="btn btn-outline-primary">Learn More</a>
            </div>
        </div>
    `;

    reportResults.style.display = 'block';
    reportResults.innerHTML = html;
}

// Show loading animation
function showLoading(container) {
    container.style.display = 'block';
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="loading"></div>
            <p class="mt-3">Searching database...</p>
        </div>
    `;
}

// Show alert message
function showAlert(container, message, type) {
    container.style.display = 'block';
    container.innerHTML = `
        <div class="alert alert-${type} fade-in">
            <i class="fas fa-exclamation-triangle me-2"></i>${message}
        </div>
    `;
}

// Utility function to capitalize first letter
function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

// Utility function to format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}
