# ScamShield - Anti-Scam & Anti-Blackmail Platform

A community-driven platform to report, check, and learn about scams and blackmail attempts. Help others stay safe by sharing your experiences.

## Features

### ✅ Core MVP Features
- **Report Scam/Blackmail**: Simple form to report scam attempts with validation
- **Scam Database Search**: Search by phone number, email, website, or message content
- **Awareness Section**: Educational content on recognizing and handling scams
- **Responsive Design**: Works on desktop, tablet, and mobile devices

### 🔧 Technical Features
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: PHP with MySQL database
- **API**: RESTful API endpoints for all operations
- **Security**: Rate limiting, input validation, SQL injection protection
- **Performance**: Optimized database queries with indexes and stored procedures

## Quick Start

### Prerequisites
- Web server with PHP 7.4+ support (Apache/Nginx)
- MySQL 5.7+ or MariaDB 10.3+
- Modern web browser

### Installation

1. **Clone/Download the project**
   ```bash
   git clone <repository-url>
   cd scamshield
   ```

2. **Set up the database**
   - Create a MySQL database named `scamshield`
   - Import the database schema:
   ```bash
   mysql -u your_username -p scamshield < database.sql
   ```

3. **Configure database connection**
   - Edit `config.php` and update the database credentials:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'scamshield');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

4. **Deploy to web server**
   - Upload all files to your web server's document root
   - Ensure PHP has write permissions for logging (optional)

5. **Test the installation**
   - Open your browser and navigate to your domain
   - Try searching for existing scam reports
   - Submit a test report to verify functionality

## File Structure

```
scamshield/
├── index.html          # Main application page
├── styles.css          # Custom CSS styles
├── script.js           # Frontend JavaScript
├── api.php             # Backend API endpoints
├── config.php          # Database configuration
├── database.sql        # Database schema and sample data
└── README.md           # This file
```

## API Endpoints

### Search Scams
- **URL**: `api.php?endpoint=search`
- **Method**: POST
- **Body**: `{"type": "phone|email|website|message", "value": "search_term"}`

### Report Scam
- **URL**: `api.php?endpoint=report`
- **Method**: POST
- **Body**: `{"type": "phone|email|website|blackmail|social|other", "contact": "contact_info", "description": "description", "amount": "amount"}`

### Get Statistics
- **URL**: `api.php?endpoint=stats`
- **Method**: GET

## Database Schema

### Main Tables
- **scam_reports**: Stores all scam reports
- **search_queries**: Tracks search analytics
- **report_feedback**: User feedback on reports
- **rate_limits**: Rate limiting data
- **activity_logs**: System activity logs

### Key Features
- Stored procedures for complex operations
- Indexes for fast searching
- Rate limiting to prevent abuse
- Activity logging for monitoring

## Security Features

- **Input Validation**: All inputs are sanitized and validated
- **Rate Limiting**: Prevents spam and abuse
- **SQL Injection Protection**: Uses prepared statements
- **CORS Configuration**: Configurable cross-origin requests
- **IP Tracking**: Monitors usage patterns

## Customization

### Adding New Scam Types
1. Update the database enum in `database.sql`
2. Add the new type to validation arrays in `api.php`
3. Update the frontend form options in `index.html`

### Styling
- Modify `styles.css` for visual customization
- Update Bootstrap classes in `index.html` for layout changes
- Customize color scheme using CSS variables in `:root`

### Rate Limiting
- Adjust limits in `config.php`:
  - `MAX_REPORTS_PER_IP_PER_DAY`
  - `MAX_SEARCHES_PER_IP_PER_HOUR`
  - `RATE_LIMIT_MAX_REQUESTS`

## Deployment Options

### Shared Hosting (cPanel)
1. Upload files via File Manager or FTP
2. Create database through cPanel
3. Import SQL file through phpMyAdmin
4. Update config.php with database details

### VPS/Dedicated Server
1. Set up LAMP/LEMP stack
2. Configure virtual host
3. Set up SSL certificate (recommended)
4. Configure firewall and security

### Cloud Platforms
- **AWS**: Use EC2 + RDS
- **Google Cloud**: Use Compute Engine + Cloud SQL
- **DigitalOcean**: Use Droplets + Managed Databases

## Monitoring and Maintenance

### Database Maintenance
- Regularly clean old rate limiting entries
- Monitor database size and performance
- Backup database regularly

### Security Monitoring
- Review activity logs for suspicious patterns
- Monitor rate limiting effectiveness
- Update PHP and database software regularly

### Performance Optimization
- Enable database query caching
- Use CDN for static assets
- Implement Redis for session storage (advanced)

## Future Enhancements

### Phase 2 Features
- User accounts and authentication
- Email notifications for new reports
- Advanced search filters
- Report verification system
- Mobile app (React Native/Flutter)

### Phase 3 Features
- AI-powered scam detection
- Browser extension
- API for third-party integrations
- Multi-language support
- Advanced analytics dashboard

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For support, please:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information
4. Contact the development team

## Disclaimer

This platform is for educational and community protection purposes. Always report serious threats to local law enforcement authorities.
