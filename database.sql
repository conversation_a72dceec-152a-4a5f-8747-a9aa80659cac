-- ScamShield Database Schema
-- This file contains the SQL commands to create the database structure

CREATE DATABASE IF NOT EXISTS scamshield;
USE scamshield;

-- Table for storing scam reports
CREATE TABLE scam_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type ENUM('phone', 'email', 'website', 'blackmail', 'social', 'other') NOT NULL,
    contact VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    amount VARCHAR(100) DEFAULT NULL,
    date_reported TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) DEFAULT NULL,
    status ENUM('pending', 'verified', 'false_positive') DEFAULT 'pending',
    report_count INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_contact (contact),
    INDEX idx_date_reported (date_reported),
    INDEX idx_status (status)
);

-- Table for tracking search queries (analytics)
CREATE TABLE search_queries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_type ENUM('phone', 'email', 'website', 'message') NOT NULL,
    search_value VARCHAR(500) NOT NULL,
    results_found INT DEFAULT 0,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_search_type (search_type),
    INDEX idx_created_at (created_at)
);

-- Table for storing feedback on reports
CREATE TABLE report_feedback (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT NOT NULL,
    feedback_type ENUM('helpful', 'not_helpful', 'false_positive') NOT NULL,
    comment TEXT DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES scam_reports(id) ON DELETE CASCADE,
    INDEX idx_report_id (report_id),
    INDEX idx_feedback_type (feedback_type)
);

-- Insert some sample data for testing
INSERT INTO scam_reports (type, contact, description, amount, status, report_count) VALUES
('phone', '******-123-4567', 'Claimed to be from IRS demanding immediate payment via gift cards. Very aggressive and threatening.', '$2000', 'verified', 5),
('email', '<EMAIL>', 'Fake lottery winning notification asking for processing fees. Claims I won $1 million.', '$500', 'verified', 12),
('website', 'fake-amazon-security.com', 'Fake Amazon security alert asking for login credentials. Looks very similar to real Amazon.', 'Account access', 'verified', 8),
('blackmail', '******-987-6543', 'Threatening to share private photos unless payment made. Claims to have hacked my devices.', '$1000', 'verified', 3),
('phone', '******-FAKE-IRS', 'Automated call claiming tax debt, demanding immediate payment via gift cards', '$3000', 'verified', 7),
('email', '<EMAIL>', 'Phishing email claiming account suspension, asking for login credentials', 'Account access', 'verified', 15),
('website', 'free-iphone-winner.net', 'Fake contest website asking for personal information to claim prize', 'Personal data', 'verified', 4),
('social', '@fake_support_team', 'Fake customer support account on social media asking for account details', 'Account access', 'verified', 6),
('blackmail', '<EMAIL>', 'Email claiming to have compromising videos, demanding Bitcoin payment', '0.5 BTC', 'verified', 2),
('phone', '******-TECH-SCAM', 'Fake tech support call claiming computer is infected, asking for remote access', '$299', 'verified', 9);

-- Create a view for easy querying of active scam reports
CREATE VIEW active_scam_reports AS
SELECT 
    id,
    type,
    contact,
    description,
    amount,
    date_reported,
    report_count,
    status
FROM scam_reports 
WHERE status IN ('pending', 'verified')
ORDER BY report_count DESC, date_reported DESC;

-- Create a view for search statistics
CREATE VIEW search_statistics AS
SELECT 
    search_type,
    COUNT(*) as total_searches,
    AVG(results_found) as avg_results_found,
    DATE(created_at) as search_date
FROM search_queries 
GROUP BY search_type, DATE(created_at)
ORDER BY search_date DESC;

-- Create indexes for better performance
CREATE INDEX idx_contact_search ON scam_reports(contact(100));
CREATE INDEX idx_description_search ON scam_reports(description(100));

-- Create a stored procedure for searching scams
DELIMITER //
CREATE PROCEDURE SearchScams(
    IN search_type VARCHAR(50),
    IN search_value VARCHAR(500)
)
BEGIN
    SELECT 
        id,
        type,
        contact,
        description,
        amount,
        date_reported,
        report_count,
        status
    FROM scam_reports 
    WHERE 
        (type = search_type OR search_type = 'message') 
        AND (
            LOWER(contact) LIKE CONCAT('%', LOWER(search_value), '%') 
            OR LOWER(description) LIKE CONCAT('%', LOWER(search_value), '%')
        )
        AND status IN ('pending', 'verified')
    ORDER BY report_count DESC, date_reported DESC
    LIMIT 20;
END //
DELIMITER ;

-- Create a stored procedure for adding new reports
DELIMITER //
CREATE PROCEDURE AddScamReport(
    IN report_type VARCHAR(50),
    IN report_contact VARCHAR(500),
    IN report_description TEXT,
    IN report_amount VARCHAR(100),
    IN reporter_ip VARCHAR(45)
)
BEGIN
    DECLARE existing_count INT DEFAULT 0;
    
    -- Check if similar report already exists
    SELECT COUNT(*) INTO existing_count
    FROM scam_reports 
    WHERE LOWER(contact) = LOWER(report_contact) 
    AND type = report_type;
    
    IF existing_count > 0 THEN
        -- Update existing report
        UPDATE scam_reports 
        SET 
            report_count = report_count + 1,
            updated_at = CURRENT_TIMESTAMP,
            description = CONCAT(description, '\n\n--- Additional Report ---\n', report_description)
        WHERE LOWER(contact) = LOWER(report_contact) 
        AND type = report_type
        LIMIT 1;
        
        SELECT 'updated' as result, existing_count + 1 as report_count;
    ELSE
        -- Insert new report
        INSERT INTO scam_reports (type, contact, description, amount, ip_address) 
        VALUES (report_type, report_contact, report_description, report_amount, reporter_ip);
        
        SELECT 'created' as result, 1 as report_count, LAST_INSERT_ID() as report_id;
    END IF;
END //
DELIMITER ;

-- Create a function to get report statistics
DELIMITER //
CREATE FUNCTION GetReportStats() 
RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE stats JSON;
    
    SELECT JSON_OBJECT(
        'total_reports', (SELECT COUNT(*) FROM scam_reports),
        'phone_scams', (SELECT COUNT(*) FROM scam_reports WHERE type = 'phone'),
        'email_scams', (SELECT COUNT(*) FROM scam_reports WHERE type = 'email'),
        'website_scams', (SELECT COUNT(*) FROM scam_reports WHERE type = 'website'),
        'blackmail_reports', (SELECT COUNT(*) FROM scam_reports WHERE type = 'blackmail'),
        'total_searches', (SELECT COUNT(*) FROM search_queries),
        'last_updated', NOW()
    ) INTO stats;
    
    RETURN stats;
END //
DELIMITER ;
