/* Custom Styles for ScamShield */

:root {
    --primary-color: #0d6efd;
    --warning-color: #ffc107;
    --success-color: #198754;
    --danger-color: #dc3545;
    --dark-color: #212529;
    --light-color: #f8f9fa;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-icon {
    font-size: 15rem;
    opacity: 0.8;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 15px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

/* Buttons */
.btn {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Alert Styles */
.alert {
    border-radius: 15px;
    border: none;
    padding: 20px;
    margin-top: 20px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Section Spacing */
section {
    padding: 80px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 40px 0;
    }
    
    .hero-icon {
        font-size: 8rem;
        margin-top: 30px;
    }
    
    .display-4 {
        font-size: 2.5rem;
    }
    
    section {
        padding: 60px 0;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom List Styles */
.list-unstyled li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.list-unstyled li:last-child {
    border-bottom: none;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Utility Classes */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #146c43 100%);
}

/* Scam Alert Styles */
.scam-alert {
    border-left: 5px solid var(--danger-color);
    background: #fff5f5;
    padding: 20px;
    margin: 20px 0;
    border-radius: 0 10px 10px 0;
}

.safe-alert {
    border-left: 5px solid var(--success-color);
    background: #f0fff4;
    padding: 20px;
    margin: 20px 0;
    border-radius: 0 10px 10px 0;
}

/* Animation for results */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Search Results */
.search-result {
    padding: 20px;
    margin: 15px 0;
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.search-result.danger {
    background: #fff5f5;
    border-color: var(--danger-color);
}

.search-result.success {
    background: #f0fff4;
    border-color: var(--success-color);
}

/* Report Success */
.report-success {
    text-align: center;
    padding: 30px;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-radius: 15px;
    margin-top: 20px;
}

.report-success i {
    font-size: 3rem;
    color: var(--success-color);
    margin-bottom: 15px;
}
