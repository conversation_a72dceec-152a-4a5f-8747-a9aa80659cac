# ScamShield Deployment Guide

## Pre-Deployment Checklist

### ✅ Files Ready
- [ ] `index.html` - Main application interface
- [ ] `styles.css` - Custom styling
- [ ] `script.js` - Frontend functionality
- [ ] `api.php` - Backend API endpoints
- [ ] `config.php` - Database configuration
- [ ] `database.sql` - Database schema and sample data
- [ ] `test.html` - Testing interface
- [ ] `README.md` - Documentation
- [ ] `DEPLOYMENT.md` - This deployment guide

### ✅ Requirements Check
- [ ] Web server with PHP 7.4+ support
- [ ] MySQL 5.7+ or MariaDB 10.3+
- [ ] SSL certificate (recommended for production)
- [ ] Domain name configured

## Deployment Steps

### Step 1: Server Setup

#### For Shared Hosting (cPanel)
1. **Access cPanel**
   - Log into your hosting control panel
   - Navigate to File Manager

2. **Upload Files**
   - Upload all project files to `public_html` or your domain folder
   - Ensure file permissions are set correctly (644 for files, 755 for directories)

#### For VPS/Dedicated Server
1. **Install LAMP Stack**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install apache2 mysql-server php php-mysql php-json php-mbstring
   
   # CentOS/RHEL
   sudo yum install httpd mysql-server php php-mysql php-json php-mbstring
   ```

2. **Configure Apache**
   ```bash
   sudo systemctl start apache2
   sudo systemctl enable apache2
   ```

### Step 2: Database Setup

1. **Create Database**
   ```sql
   CREATE DATABASE scamshield;
   CREATE USER 'scamshield_user'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON scamshield.* TO 'scamshield_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **Import Schema**
   ```bash
   mysql -u scamshield_user -p scamshield < database.sql
   ```

3. **Verify Installation**
   ```sql
   USE scamshield;
   SHOW TABLES;
   SELECT COUNT(*) FROM scam_reports;
   ```

### Step 3: Configuration

1. **Update Database Credentials**
   Edit `config.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'scamshield');
   define('DB_USER', 'scamshield_user');
   define('DB_PASS', 'your_secure_password');
   ```

2. **Security Settings**
   ```php
   // Set to false in production
   define('APP_DEBUG', false);
   
   // Add your domain
   define('ALLOWED_ORIGINS', [
       'https://your-domain.com',
       'https://www.your-domain.com'
   ]);
   ```

3. **File Permissions**
   ```bash
   chmod 644 *.php *.html *.css *.js *.md *.sql
   chmod 600 config.php  # More restrictive for config
   ```

### Step 4: Testing

1. **Open Test Page**
   - Navigate to `https://your-domain.com/test.html`
   - Run all API tests
   - Verify all tests pass

2. **Test Main Application**
   - Open `https://your-domain.com/index.html`
   - Test search functionality
   - Test report submission
   - Verify responsive design on mobile

3. **Database Verification**
   ```sql
   -- Check if test data was inserted
   SELECT * FROM scam_reports ORDER BY created_at DESC LIMIT 5;
   
   -- Check search queries are logged
   SELECT * FROM search_queries ORDER BY created_at DESC LIMIT 5;
   ```

### Step 5: Security Hardening

1. **SSL Certificate**
   ```bash
   # Using Let's Encrypt (free)
   sudo apt install certbot python3-certbot-apache
   sudo certbot --apache -d your-domain.com
   ```

2. **Apache Security Headers**
   Add to `.htaccess`:
   ```apache
   Header always set X-Content-Type-Options nosniff
   Header always set X-Frame-Options DENY
   Header always set X-XSS-Protection "1; mode=block"
   Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
   Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com; font-src 'self' cdnjs.cloudflare.com; img-src 'self' data:;"
   ```

3. **Database Security**
   ```sql
   -- Remove test user if created
   DROP USER IF EXISTS 'test'@'localhost';
   
   -- Ensure root access is restricted
   DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');
   FLUSH PRIVILEGES;
   ```

### Step 6: Performance Optimization

1. **Enable Gzip Compression**
   Add to `.htaccess`:
   ```apache
   <IfModule mod_deflate.c>
       AddOutputFilterByType DEFLATE text/plain
       AddOutputFilterByType DEFLATE text/html
       AddOutputFilterByType DEFLATE text/xml
       AddOutputFilterByType DEFLATE text/css
       AddOutputFilterByType DEFLATE application/xml
       AddOutputFilterByType DEFLATE application/xhtml+xml
       AddOutputFilterByType DEFLATE application/rss+xml
       AddOutputFilterByType DEFLATE application/javascript
       AddOutputFilterByType DEFLATE application/x-javascript
   </IfModule>
   ```

2. **Browser Caching**
   ```apache
   <IfModule mod_expires.c>
       ExpiresActive On
       ExpiresByType text/css "access plus 1 month"
       ExpiresByType application/javascript "access plus 1 month"
       ExpiresByType image/png "access plus 1 month"
       ExpiresByType image/jpg "access plus 1 month"
       ExpiresByType image/jpeg "access plus 1 month"
   </IfModule>
   ```

3. **Database Optimization**
   ```sql
   -- Optimize tables
   OPTIMIZE TABLE scam_reports, search_queries, report_feedback;
   
   -- Analyze tables for better query planning
   ANALYZE TABLE scam_reports, search_queries, report_feedback;
   ```

## Post-Deployment Monitoring

### Daily Tasks
- [ ] Check error logs for issues
- [ ] Monitor database size
- [ ] Review rate limiting effectiveness

### Weekly Tasks
- [ ] Backup database
- [ ] Review activity logs
- [ ] Check for software updates

### Monthly Tasks
- [ ] Analyze usage statistics
- [ ] Clean old rate limiting data
- [ ] Review and update security measures

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check credentials in `config.php`
   - Verify MySQL service is running
   - Check firewall settings

2. **API Endpoints Not Working**
   - Verify PHP is installed and working
   - Check Apache mod_rewrite is enabled
   - Review error logs

3. **Search Returns No Results**
   - Verify sample data was imported
   - Check database connection
   - Test with known scam data

4. **Rate Limiting Too Strict**
   - Adjust limits in `config.php`
   - Clear rate limiting table if needed

### Log Locations
- **Apache Error Log**: `/var/log/apache2/error.log`
- **PHP Error Log**: `/var/log/php_errors.log`
- **MySQL Error Log**: `/var/log/mysql/error.log`

## Backup Strategy

### Database Backup
```bash
# Daily backup
mysqldump -u scamshield_user -p scamshield > backup_$(date +%Y%m%d).sql

# Automated backup script
#!/bin/bash
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u scamshield_user -p scamshield | gzip > $BACKUP_DIR/scamshield_$DATE.sql.gz
find $BACKUP_DIR -name "scamshield_*.sql.gz" -mtime +30 -delete
```

### File Backup
```bash
# Backup application files
tar -czf scamshield_files_$(date +%Y%m%d).tar.gz *.php *.html *.css *.js *.md
```

## Scaling Considerations

### For High Traffic
1. **Database Optimization**
   - Add read replicas
   - Implement connection pooling
   - Use Redis for caching

2. **Load Balancing**
   - Multiple web servers
   - Database clustering
   - CDN for static assets

3. **Monitoring**
   - Application performance monitoring
   - Database performance monitoring
   - Real-time alerting

## Support and Maintenance

### Getting Help
1. Check the README.md for basic troubleshooting
2. Review error logs for specific issues
3. Test with the test.html page
4. Contact development team with detailed error information

### Maintenance Schedule
- **Daily**: Monitor logs and performance
- **Weekly**: Security updates and backups
- **Monthly**: Performance review and optimization
- **Quarterly**: Security audit and feature updates
