<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScamShield - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <h1>ScamShield - API Test Page</h1>
    <p>This page tests the ScamShield API functionality without requiring a database setup.</p>
    
    <div class="test-section">
        <h2>Test 1: Search API</h2>
        <p>Test searching for scams in the database.</p>
        <button class="test-button" onclick="testSearch()">Test Search</button>
        <div id="searchResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Report API</h2>
        <p>Test submitting a scam report.</p>
        <button class="test-button" onclick="testReport()">Test Report</button>
        <div id="reportResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Statistics API</h2>
        <p>Test getting database statistics.</p>
        <button class="test-button" onclick="testStats()">Test Stats</button>
        <div id="statsResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 4: Frontend Functionality</h2>
        <p>Test the main application interface.</p>
        <a href="index.html" class="test-button" style="text-decoration: none; display: inline-block;">Open Main App</a>
    </div>

    <script>
        // API Configuration
        const API_BASE_URL = 'api.php';

        // API Helper Function
        async function makeAPIRequest(endpoint, data = null, method = 'GET') {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data && method !== 'GET') {
                    options.body = JSON.stringify(data);
                }
                
                const url = `${API_BASE_URL}?endpoint=${endpoint}`;
                const response = await fetch(url, options);
                
                const result = await response.json();
                return { success: response.ok, data: result, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Test Search Functionality
        async function testSearch() {
            const resultDiv = document.getElementById('searchResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing search API...';
            resultDiv.className = 'result';
            
            const testData = {
                type: 'phone',
                value: '******-123-4567'
            };
            
            const result = await makeAPIRequest('search', testData, 'POST');
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ Search API Test Passed</strong><br>
                    Status: ${result.status}<br>
                    Results found: ${result.data.count || 0}<br>
                    Response: <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ Search API Test Failed</strong><br>
                    Status: ${result.status}<br>
                    Error: ${result.error || result.data?.error || 'Unknown error'}<br>
                    Response: <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            }
        }

        // Test Report Functionality
        async function testReport() {
            const resultDiv = document.getElementById('reportResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing report API...';
            resultDiv.className = 'result';
            
            const testData = {
                type: 'phone',
                contact: '******-TEST-123',
                description: 'This is a test scam report from the test page.',
                amount: '$100'
            };
            
            const result = await makeAPIRequest('report', testData, 'POST');
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ Report API Test Passed</strong><br>
                    Status: ${result.status}<br>
                    Report ID: ${result.data.report_id || 'N/A'}<br>
                    Response: <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ Report API Test Failed</strong><br>
                    Status: ${result.status}<br>
                    Error: ${result.error || result.data?.error || 'Unknown error'}<br>
                    Response: <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            }
        }

        // Test Statistics Functionality
        async function testStats() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing stats API...';
            resultDiv.className = 'result';
            
            const result = await makeAPIRequest('stats');
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ Stats API Test Passed</strong><br>
                    Status: ${result.status}<br>
                    Total Reports: ${result.data.stats?.total_reports || 'N/A'}<br>
                    Response: <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ Stats API Test Failed</strong><br>
                    Status: ${result.status}<br>
                    Error: ${result.error || result.data?.error || 'Unknown error'}<br>
                    Response: <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            }
        }

        // Auto-run basic connectivity test on page load
        window.addEventListener('load', function() {
            console.log('ScamShield Test Page Loaded');
            console.log('Ready to test API endpoints');
        });
    </script>
</body>
</html>
