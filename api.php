<?php
/**
 * ScamShield API Endpoints
 * Handles all API requests for the ScamShield platform
 */

require_once 'config.php';

// Handle CORS
handleCORS();

// Get request method and endpoint
$method = $_SERVER['REQUEST_METHOD'];
$endpoint = $_GET['endpoint'] ?? '';
$clientIP = getClientIP();

// Rate limiting
if (!checkRateLimit($clientIP, $endpoint)) {
    sendJSONResponse([
        'success' => false,
        'error' => 'Rate limit exceeded. Please try again later.',
        'code' => 'RATE_LIMIT_EXCEEDED'
    ], 429);
}

// Route requests
switch ($endpoint) {
    case 'search':
        if ($method === 'POST') {
            handleSearchRequest();
        } else {
            sendJSONResponse(['success' => false, 'error' => 'Method not allowed'], 405);
        }
        break;
        
    case 'report':
        if ($method === 'POST') {
            handleReportRequest();
        } else {
            sendJSONResponse(['success' => false, 'error' => 'Method not allowed'], 405);
        }
        break;
        
    case 'stats':
        if ($method === 'GET') {
            handleStatsRequest();
        } else {
            sendJSONResponse(['success' => false, 'error' => 'Method not allowed'], 405);
        }
        break;
        
    default:
        sendJSONResponse(['success' => false, 'error' => 'Endpoint not found'], 404);
}

/**
 * Handle search requests
 */
function handleSearchRequest() {
    global $clientIP;
    
    // Get and validate input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJSONResponse(['success' => false, 'error' => 'Invalid JSON input'], 400);
    }
    
    $searchType = sanitizeInput($input['type'] ?? '');
    $searchValue = sanitizeInput($input['value'] ?? '');
    
    // Validate required fields
    if (empty($searchType) || empty($searchValue)) {
        sendJSONResponse([
            'success' => false,
            'error' => 'Missing required fields: type and value'
        ], 400);
    }
    
    // Validate search type
    $validTypes = ['phone', 'email', 'website', 'message'];
    if (!in_array($searchType, $validTypes)) {
        sendJSONResponse([
            'success' => false,
            'error' => 'Invalid search type. Must be one of: ' . implode(', ', $validTypes)
        ], 400);
    }
    
    // Additional validation based on type
    if ($searchType === 'email' && !isValidEmail($searchValue)) {
        sendJSONResponse([
            'success' => false,
            'error' => 'Invalid email format'
        ], 400);
    }
    
    if ($searchType === 'phone' && !isValidPhone($searchValue)) {
        sendJSONResponse([
            'success' => false,
            'error' => 'Invalid phone number format'
        ], 400);
    }
    
    if ($searchType === 'website' && !isValidURL($searchValue)) {
        sendJSONResponse([
            'success' => false,
            'error' => 'Invalid URL format'
        ], 400);
    }
    
    try {
        $db = Database::getInstance()->getConnection();
        
        // Log the search query
        $stmt = $db->prepare("INSERT INTO search_queries (search_type, search_value, ip_address, user_agent) VALUES (?, ?, ?, ?)");
        $stmt->execute([$searchType, $searchValue, $clientIP, $_SERVER['HTTP_USER_AGENT'] ?? '']);
        
        // Search for scams
        $stmt = $db->prepare("CALL SearchScams(?, ?)");
        $stmt->execute([$searchType, $searchValue]);
        $results = $stmt->fetchAll();
        
        // Update search query with results count
        $stmt = $db->prepare("UPDATE search_queries SET results_found = ? WHERE ip_address = ? ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([count($results), $clientIP]);
        
        // Log activity
        logActivity('search', "Type: $searchType, Value: $searchValue, Results: " . count($results), $clientIP);
        
        sendJSONResponse([
            'success' => true,
            'results' => $results,
            'count' => count($results),
            'search_type' => $searchType,
            'search_value' => $searchValue
        ]);
        
    } catch (Exception $e) {
        logActivity('search_error', $e->getMessage(), $clientIP);
        
        sendJSONResponse([
            'success' => false,
            'error' => APP_DEBUG ? $e->getMessage() : 'Search failed. Please try again.'
        ], 500);
    }
}

/**
 * Handle report requests
 */
function handleReportRequest() {
    global $clientIP;
    
    // Check daily report limit
    if (!checkDailyReportLimit($clientIP)) {
        sendJSONResponse([
            'success' => false,
            'error' => 'Daily report limit exceeded. Please try again tomorrow.',
            'code' => 'DAILY_LIMIT_EXCEEDED'
        ], 429);
    }
    
    // Get and validate input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJSONResponse(['success' => false, 'error' => 'Invalid JSON input'], 400);
    }
    
    $reportType = sanitizeInput($input['type'] ?? '');
    $reportContact = sanitizeInput($input['contact'] ?? '');
    $reportDescription = sanitizeInput($input['description'] ?? '');
    $reportAmount = sanitizeInput($input['amount'] ?? '');
    
    // Validate required fields
    if (empty($reportType) || empty($reportContact) || empty($reportDescription)) {
        sendJSONResponse([
            'success' => false,
            'error' => 'Missing required fields: type, contact, and description'
        ], 400);
    }
    
    // Validate report type
    $validTypes = ['phone', 'email', 'website', 'blackmail', 'social', 'other'];
    if (!in_array($reportType, $validTypes)) {
        sendJSONResponse([
            'success' => false,
            'error' => 'Invalid report type. Must be one of: ' . implode(', ', $validTypes)
        ], 400);
    }
    
    // Validate description length
    if (strlen($reportDescription) < 10) {
        sendJSONResponse([
            'success' => false,
            'error' => 'Description must be at least 10 characters long'
        ], 400);
    }
    
    if (strlen($reportDescription) > 2000) {
        sendJSONResponse([
            'success' => false,
            'error' => 'Description must be less than 2000 characters'
        ], 400);
    }
    
    try {
        $db = Database::getInstance()->getConnection();
        
        // Add the report using stored procedure
        $stmt = $db->prepare("CALL AddScamReport(?, ?, ?, ?, ?)");
        $stmt->execute([$reportType, $reportContact, $reportDescription, $reportAmount, $clientIP]);
        $result = $stmt->fetch();
        
        // Log activity
        logActivity('report', "Type: $reportType, Contact: $reportContact", $clientIP);
        
        sendJSONResponse([
            'success' => true,
            'message' => 'Report submitted successfully',
            'result' => $result['result'],
            'report_count' => $result['report_count'],
            'report_id' => $result['report_id'] ?? null
        ]);
        
    } catch (Exception $e) {
        logActivity('report_error', $e->getMessage(), $clientIP);
        
        sendJSONResponse([
            'success' => false,
            'error' => APP_DEBUG ? $e->getMessage() : 'Report submission failed. Please try again.'
        ], 500);
    }
}

/**
 * Handle statistics requests
 */
function handleStatsRequest() {
    try {
        $db = Database::getInstance()->getConnection();
        
        // Get basic statistics
        $stmt = $db->query("SELECT GetReportStats() as stats");
        $result = $stmt->fetch();
        $stats = json_decode($result['stats'], true);
        
        // Get recent activity
        $stmt = $db->query("
            SELECT 
                DATE(date_reported) as date,
                COUNT(*) as reports
            FROM scam_reports 
            WHERE date_reported >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(date_reported)
            ORDER BY date DESC
            LIMIT 30
        ");
        $recentActivity = $stmt->fetchAll();
        
        // Get top scam types
        $stmt = $db->query("
            SELECT 
                type,
                COUNT(*) as count,
                SUM(report_count) as total_reports
            FROM scam_reports 
            GROUP BY type
            ORDER BY total_reports DESC
        ");
        $scamTypes = $stmt->fetchAll();
        
        sendJSONResponse([
            'success' => true,
            'stats' => $stats,
            'recent_activity' => $recentActivity,
            'scam_types' => $scamTypes
        ]);
        
    } catch (Exception $e) {
        sendJSONResponse([
            'success' => false,
            'error' => APP_DEBUG ? $e->getMessage() : 'Failed to fetch statistics'
        ], 500);
    }
}

/**
 * Check daily report limit for IP
 */
function checkDailyReportLimit($ip) {
    try {
        $db = Database::getInstance()->getConnection();
        
        $stmt = $db->prepare("
            SELECT COUNT(*) as count 
            FROM scam_reports 
            WHERE ip_address = ? 
            AND DATE(created_at) = CURDATE()
        ");
        $stmt->execute([$ip]);
        $result = $stmt->fetch();
        
        return $result['count'] < MAX_REPORTS_PER_IP_PER_DAY;
    } catch (Exception $e) {
        // If check fails, allow the request
        return true;
    }
}
?>
