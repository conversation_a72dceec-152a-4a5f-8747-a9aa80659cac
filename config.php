<?php
/**
 * ScamShield Configuration File
 * Contains database connection settings and application configuration
 */

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'scamshield');
define('DB_USER', 'root'); // Change this to your database username
define('DB_PASS', ''); // Change this to your database password
define('DB_CHARSET', 'utf8mb4');

// Application Configuration
define('APP_NAME', 'ScamShield');
define('APP_VERSION', '1.0.0');
define('APP_DEBUG', true); // Set to false in production

// Security Configuration
define('MAX_REPORTS_PER_IP_PER_DAY', 10);
define('MAX_SEARCHES_PER_IP_PER_HOUR', 100);

// CORS Configuration (for API access)
define('ALLOWED_ORIGINS', [
    'http://localhost',
    'http://127.0.0.1',
    'https://your-domain.com' // Add your production domain here
]);

// Rate Limiting Configuration
define('RATE_LIMIT_ENABLED', true);
define('RATE_LIMIT_WINDOW', 3600); // 1 hour in seconds
define('RATE_LIMIT_MAX_REQUESTS', 100);

/**
 * Database Connection Class
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            if (APP_DEBUG) {
                die("Database connection failed: " . $e->getMessage());
            } else {
                die("Database connection failed. Please try again later.");
            }
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
}

/**
 * Utility Functions
 */

/**
 * Get client IP address
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    
    return $data;
}

/**
 * Validate email address
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (basic validation)
 */
function isValidPhone($phone) {
    // Remove all non-digit characters
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    
    // Check if it's a valid length (7-15 digits, optionally starting with +)
    return preg_match('/^\+?[0-9]{7,15}$/', $phone);
}

/**
 * Validate URL
 */
function isValidURL($url) {
    // Add protocol if missing
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Rate limiting function
 */
function checkRateLimit($ip, $action = 'general') {
    if (!RATE_LIMIT_ENABLED) {
        return true;
    }
    
    try {
        $db = Database::getInstance()->getConnection();
        
        // Clean old entries
        $stmt = $db->prepare("DELETE FROM rate_limits WHERE created_at < DATE_SUB(NOW(), INTERVAL ? SECOND)");
        $stmt->execute([RATE_LIMIT_WINDOW]);
        
        // Check current rate
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM rate_limits WHERE ip_address = ? AND action = ? AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)");
        $stmt->execute([$ip, $action, RATE_LIMIT_WINDOW]);
        $result = $stmt->fetch();
        
        if ($result['count'] >= RATE_LIMIT_MAX_REQUESTS) {
            return false;
        }
        
        // Log this request
        $stmt = $db->prepare("INSERT INTO rate_limits (ip_address, action) VALUES (?, ?)");
        $stmt->execute([$ip, $action]);
        
        return true;
    } catch (Exception $e) {
        // If rate limiting fails, allow the request
        return true;
    }
}

/**
 * Log activity
 */
function logActivity($action, $details = '', $ip = null) {
    if ($ip === null) {
        $ip = getClientIP();
    }
    
    try {
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("INSERT INTO activity_logs (action, details, ip_address) VALUES (?, ?, ?)");
        $stmt->execute([$action, $details, $ip]);
    } catch (Exception $e) {
        // Silently fail if logging doesn't work
        if (APP_DEBUG) {
            error_log("Failed to log activity: " . $e->getMessage());
        }
    }
}

/**
 * Send JSON response
 */
function sendJSONResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
    
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

/**
 * Handle CORS preflight requests
 */
function handleCORS() {
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type');
        header('Access-Control-Max-Age: 86400');
        exit;
    }
}

// Create rate_limits table if it doesn't exist
try {
    $db = Database::getInstance()->getConnection();
    $db->exec("CREATE TABLE IF NOT EXISTS rate_limits (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ip_address VARCHAR(45) NOT NULL,
        action VARCHAR(50) NOT NULL DEFAULT 'general',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_ip_action_time (ip_address, action, created_at)
    )");
    
    $db->exec("CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        action VARCHAR(100) NOT NULL,
        details TEXT,
        ip_address VARCHAR(45),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_action_time (action, created_at)
    )");
} catch (Exception $e) {
    if (APP_DEBUG) {
        error_log("Failed to create utility tables: " . $e->getMessage());
    }
}

// Set error reporting based on debug mode
if (APP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}
?>
